"""
Simple version of Personal Growth Architect API for testing
"""

from fastapi import FastAPI
from pydantic import BaseModel
from typing import List, Dict, Any

app = FastAPI(
    title="Personal Growth Architect - Simple",
    description="Simplified version for testing",
    version="1.0.0"
)

class GoalCreate(BaseModel):
    user_id: str
    title: str
    description: str
    category: str
    priority: int = 1

class GoalResponse(BaseModel):
    id: int
    user_id: str
    title: str
    description: str
    category: str
    status: str = "active"

# In-memory storage for testing
goals_db = []
goal_counter = 1

@app.get("/")
async def root():
    return {"message": "Personal Growth Architect API is running (Simple Version)"}

@app.post("/create_goal", response_model=GoalResponse)
async def create_goal(goal: GoalCreate):
    global goal_counter
    
    new_goal = GoalResponse(
        id=goal_counter,
        user_id=goal.user_id,
        title=goal.title,
        description=goal.description,
        category=goal.category,
        status="active"
    )
    
    goals_db.append(new_goal.dict())
    goal_counter += 1
    
    return new_goal

@app.get("/goals/{user_id}")
async def get_user_goals(user_id: str):
    user_goals = [goal for goal in goals_db if goal["user_id"] == user_id]
    return user_goals

@app.post("/get_daily_plan")
async def get_daily_plan(request: Dict[str, Any]):
    return {
        "date": request.get("date", "2024-12-06"),
        "tasks": [
            {
                "name": "Sample task",
                "description": "This is a test task",
                "estimated_minutes": 30,
                "category": "general"
            }
        ],
        "calendar_events": [],
        "expert_advice": "This is a simplified version for testing.",
        "estimated_duration": 0.5
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
