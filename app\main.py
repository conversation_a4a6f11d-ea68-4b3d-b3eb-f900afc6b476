"""
Personal Growth Architect - FastAPI Main Application

This module contains the FastAPI application with endpoints for:
- Creating and managing personal growth goals
- Generating daily plans through the agentic workflow
- Processing user feedback for plan adaptation
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

try:
    from app.graph import PersonalGrowthWorkflow
    LANGGRAPH_AVAILABLE = True
    # Temporarily disable Lang<PERSON>raph due to issues
    LANGGRAPH_AVAILABLE = False
    print("LangGraph temporarily disabled - using simplified workflow")
except ImportError as e:
    print(f"LangGraph not available: {e}")
    LANGGRAPH_AVAILABLE = False

from app.simple_workflow import SimplePersonalGrowthWorkflow
from app.db.database import get_db, init_db
from app.db.models import Goal, User
from sqlalchemy.orm import Session

# Load environment variables
load_dotenv()

# Initialize database on startup using lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    try:
        init_db()
        print("Database initialized successfully")
    except Exception as e:
        print(f"Database initialization failed: {e}")
        # Don't raise the exception to allow the app to start
    yield
    # Shutdown
    print("Application shutting down")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="Personal Growth Architect",
    description="AI Life Coach Backend - Agentic system for personal growth planning",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



# Pydantic models for request/response
class GoalCreate(BaseModel):
    user_id: str
    title: str
    description: str
    category: str  # e.g., "fitness", "learning", "career"
    target_date: Optional[str] = None
    priority: int = 1  # 1-5 scale

class GoalResponse(BaseModel):
    id: int
    user_id: str
    title: str
    description: str
    category: str
    status: str
    created_at: str

class DailyPlanRequest(BaseModel):
    user_id: str
    goal_ids: List[int]
    date: str  # YYYY-MM-DD format
    available_hours: float = 2.0
    preferences: Optional[Dict[str, Any]] = None

class DailyPlanResponse(BaseModel):
    date: str
    tasks: List[Dict[str, Any]]
    calendar_events: List[Dict[str, Any]]
    expert_advice: str
    estimated_duration: float

class FeedbackSubmission(BaseModel):
    user_id: str
    date: str
    completed_tasks: List[str]
    difficulty_rating: int  # 1-5 scale
    satisfaction_rating: int  # 1-5 scale
    notes: Optional[str] = None

# Initialize the workflow (will be created when needed)
workflow = None

def get_workflow():
    """Get or create the workflow instance."""
    global workflow
    if workflow is None:
        try:
            if LANGGRAPH_AVAILABLE:
                workflow = PersonalGrowthWorkflow()
                print("Using LangGraph workflow")
            else:
                workflow = SimplePersonalGrowthWorkflow()
                print("Using simplified workflow")
        except Exception as e:
            print(f"Warning: Could not initialize LangGraph workflow: {e}")
            print("Falling back to simplified workflow")
            workflow = SimplePersonalGrowthWorkflow()
    return workflow

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Personal Growth Architect API is running"}

@app.post("/create_goal", response_model=GoalResponse)
async def create_goal(goal: GoalCreate, db: Session = Depends(get_db)):
    """
    Create a new personal growth goal
    
    This endpoint accepts a goal and stores it in the database.
    The goal will be processed by the agentic workflow when generating daily plans.
    """
    try:
        # Create new goal in database
        db_goal = Goal(
            user_id=goal.user_id,
            title=goal.title,
            description=goal.description,
            category=goal.category,
            target_date=goal.target_date,
            priority=goal.priority,
            status="active"
        )
        db.add(db_goal)
        db.commit()
        db.refresh(db_goal)
        
        return GoalResponse(
            id=db_goal.id,
            user_id=db_goal.user_id,
            title=db_goal.title,
            description=db_goal.description,
            category=db_goal.category,
            status=db_goal.status,
            created_at=str(db_goal.created_at)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating goal: {str(e)}")

@app.post("/get_daily_plan", response_model=DailyPlanResponse)
async def get_daily_plan(request: DailyPlanRequest, db: Session = Depends(get_db)):
    """
    Generate a daily plan using the agentic workflow
    
    This endpoint triggers the LangGraph workflow that:
    1. Clarifies goals and breaks them into tasks
    2. Gets expert advice from specialist agents
    3. Creates a time-blocked schedule
    4. Integrates with Google Calendar
    """
    try:
        # Fetch goals from database
        goals = db.query(Goal).filter(
            Goal.id.in_(request.goal_ids),
            Goal.user_id == request.user_id,
            Goal.status == "active"
        ).all()

        print(f"DEBUG: Found {len(goals)} goals for user {request.user_id}")
        print(f"DEBUG: Goal IDs requested: {request.goal_ids}")

        if not goals:
            raise HTTPException(status_code=404, detail="No active goals found")

        # Prepare workflow input
        workflow_input = {
            "user_id": request.user_id,
            "goals": [{"id": g.id, "title": g.title, "description": g.description,
                      "category": g.category} for g in goals],
            "date": request.date,
            "available_hours": request.available_hours,
            "preferences": request.preferences or {}
        }

        print(f"DEBUG: Workflow input: {workflow_input}")
        
        # Execute the agentic workflow
        current_workflow = get_workflow()
        print(f"DEBUG: Workflow instance: {type(current_workflow)}")

        if current_workflow is None:
            print("DEBUG: Workflow is None, using fallback")
            # Fallback response if workflow is not available
            return DailyPlanResponse(
                date=request.date,
                tasks=[{
                    "name": f"Work on: {goal.title}",
                    "description": goal.description,
                    "estimated_minutes": 60,
                    "category": goal.category
                } for goal in goals],
                calendar_events=[],
                expert_advice="Workflow temporarily unavailable. Basic plan generated.",
                estimated_duration=len(goals) * 1.0
            )

        print("DEBUG: Executing workflow...")
        result = await current_workflow.execute(workflow_input)
        print(f"DEBUG: Workflow result: {result}")
        
        return DailyPlanResponse(
            date=request.date,
            tasks=result.get("tasks", []),
            calendar_events=result.get("calendar_events", []),
            expert_advice=result.get("expert_advice", ""),
            estimated_duration=result.get("estimated_duration", 0.0)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating daily plan: {str(e)}")

@app.post("/submit_feedback")
async def submit_feedback(feedback: FeedbackSubmission, db: Session = Depends(get_db)):
    """
    Submit daily feedback for plan adaptation
    
    This endpoint processes user feedback and triggers the Feedback_Analyst agent
    to adapt future plans based on user performance and satisfaction.
    """
    try:
        # Process feedback through the workflow
        feedback_input = {
            "user_id": feedback.user_id,
            "date": feedback.date,
            "completed_tasks": feedback.completed_tasks,
            "difficulty_rating": feedback.difficulty_rating,
            "satisfaction_rating": feedback.satisfaction_rating,
            "notes": feedback.notes
        }
        
        # Execute feedback analysis workflow
        current_workflow = get_workflow()
        if current_workflow is None:
            return {
                "message": "Feedback received but workflow temporarily unavailable",
                "adaptations": ["Will process when system is available"],
                "recommendations": "Thank you for your feedback. We'll use it to improve future plans."
            }

        analysis_result = await current_workflow.process_feedback(feedback_input)
        
        return {
            "message": "Feedback processed successfully",
            "adaptations": analysis_result.get("adaptations", []),
            "recommendations": analysis_result.get("recommendations", "")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing feedback: {str(e)}")

@app.get("/goals/{user_id}")
async def get_user_goals(user_id: str, db: Session = Depends(get_db)):
    """Get all goals for a specific user"""
    goals = db.query(Goal).filter(Goal.user_id == user_id).all()
    return [GoalResponse(
        id=g.id,
        user_id=g.user_id,
        title=g.title,
        description=g.description,
        category=g.category,
        status=g.status,
        created_at=str(g.created_at)
    ) for g in goals]

@app.post("/test_workflow")
async def test_workflow():
    """Test endpoint to debug workflow"""
    try:
        print("DEBUG: test_workflow called")
        workflow = get_workflow()
        print(f"DEBUG: Got workflow: {type(workflow)}")

        if workflow is None:
            return {"error": "Workflow is None"}

        test_input = {
            "user_id": "test",
            "goals": [{"id": 1, "title": "Test Goal", "description": "Test Description", "category": "fitness"}],
            "date": "2024-12-06",
            "available_hours": 1.0,
            "preferences": {}
        }

        print(f"DEBUG: About to execute workflow with: {test_input}")
        result = await workflow.execute(test_input)
        print(f"DEBUG: Workflow returned: {result}")
        return {"status": "success", "result": result}

    except Exception as e:
        import traceback
        print(f"DEBUG: Exception in test_workflow: {e}")
        print(f"DEBUG: Full traceback: {traceback.format_exc()}")
        return {"error": f"Workflow test failed: {str(e)}", "type": str(type(e))}

@app.post("/test_simple")
async def test_simple():
    """Super simple test"""
    try:
        from app.simple_workflow import SimplePersonalGrowthWorkflow
        workflow = SimplePersonalGrowthWorkflow()

        test_input = {
            "user_id": "test",
            "goals": [{"id": 1, "title": "Test Goal", "description": "Test Description", "category": "fitness"}],
            "date": "2024-12-06",
            "available_hours": 1.0,
            "preferences": {}
        }

        result = await workflow.execute(test_input)
        return {"status": "success", "result": result}

    except Exception as e:
        import traceback
        return {"error": f"Simple test failed: {str(e)}", "traceback": traceback.format_exc()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
