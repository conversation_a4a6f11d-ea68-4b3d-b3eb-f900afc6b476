"""
Personal Growth Architect - SQLAlchemy ORM Models

This module defines the database models for the Personal Growth Architect
application using SQLAlchemy ORM. Models include:

- User: User account information
- Goal: Personal growth goals
- Task: Individual tasks derived from goals
- Feedback: User feedback on completed tasks
- CalendarEvent: Calendar integration tracking
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class User(Base):
    """
    User model for storing user account information.
    
    This model stores basic user information and preferences
    for the personal growth planning system.
    """
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), unique=True, index=True, nullable=False)  # External user ID
    email = Column(String(255), unique=True, index=True)
    name = Column(String(255))
    timezone = Column(String(50), default="UTC")
    preferences = Column(JSON, default=dict)  # User preferences as JSON
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    goals = relationship("Goal", back_populates="user", cascade="all, delete-orphan")
    feedback = relationship("Feedback", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(user_id='{self.user_id}', email='{self.email}')>"

class Goal(Base):
    """
    Goal model for storing personal growth goals.
    
    This model represents high-level goals that users want to achieve,
    which will be broken down into actionable tasks.
    """
    __tablename__ = "goals"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), ForeignKey("users.user_id"), nullable=False)
    
    # Goal details
    title = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(100), nullable=False)  # fitness, learning, career, etc.
    priority = Column(Integer, default=1)  # 1-5 scale
    status = Column(String(50), default="active")  # active, completed, paused, cancelled
    
    # Timeline
    target_date = Column(String(20))  # YYYY-MM-DD format
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    
    # Metadata
    clarified_data = Column(JSON)  # Stores clarified goal information from Goal_Clarifier agent
    
    # Relationships
    user = relationship("User", back_populates="goals")
    tasks = relationship("Task", back_populates="goal", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Goal(id={self.id}, title='{self.title}', category='{self.category}')>"

class Task(Base):
    """
    Task model for storing individual actionable tasks.
    
    Tasks are derived from goals and represent specific actions
    that can be completed in a single session.
    """
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    goal_id = Column(Integer, ForeignKey("goals.id"), nullable=False)
    user_id = Column(String(255), ForeignKey("users.user_id"), nullable=False)
    
    # Task details
    name = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(100))
    
    # Scheduling and time management
    estimated_minutes = Column(Integer, default=30)
    actual_minutes = Column(Integer)
    difficulty = Column(Integer, default=3)  # 1-5 scale
    
    # Status and completion
    status = Column(String(50), default="pending")  # pending, in_progress, completed, skipped
    scheduled_date = Column(String(20))  # YYYY-MM-DD format
    scheduled_time = Column(String(20))  # HH:MM format
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # Expert advice and guidance
    expert_advice = Column(Text)  # Advice from specialist agents
    
    # Relationships
    goal = relationship("Goal", back_populates="tasks")
    user = relationship("User")
    calendar_events = relationship("CalendarEvent", back_populates="task", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Task(id={self.id}, name='{self.name}', status='{self.status}')>"

class Feedback(Base):
    """
    Feedback model for storing user feedback on completed tasks and plans.
    
    This model captures user feedback that is used by the Feedback_Analyst
    agent to improve future planning and task recommendations.
    """
    __tablename__ = "feedback"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), ForeignKey("users.user_id"), nullable=False)
    
    # Feedback context
    date = Column(String(20), nullable=False)  # YYYY-MM-DD format
    task_ids = Column(JSON)  # List of task IDs this feedback relates to
    
    # Feedback ratings
    difficulty_rating = Column(Integer)  # 1-5 scale (1=too easy, 5=too hard)
    satisfaction_rating = Column(Integer)  # 1-5 scale (1=unsatisfied, 5=very satisfied)
    completion_rate = Column(Float)  # Percentage of planned tasks completed
    
    # Qualitative feedback
    notes = Column(Text)
    completed_tasks = Column(JSON)  # List of completed task names/IDs
    skipped_tasks = Column(JSON)  # List of skipped task names/IDs with reasons
    
    # Time tracking
    planned_time_minutes = Column(Integer)
    actual_time_minutes = Column(Integer)
    
    # Adaptation suggestions (from Feedback_Analyst)
    suggested_adaptations = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="feedback")
    
    def __repr__(self):
        return f"<Feedback(id={self.id}, date='{self.date}', satisfaction={self.satisfaction_rating})>"

class CalendarEvent(Base):
    """
    CalendarEvent model for tracking Google Calendar integration.
    
    This model stores information about calendar events created for tasks,
    enabling synchronization and conflict detection.
    """
    __tablename__ = "calendar_events"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    user_id = Column(String(255), ForeignKey("users.user_id"), nullable=False)
    
    # Google Calendar details
    google_event_id = Column(String(255), unique=True)  # Google Calendar event ID
    calendar_id = Column(String(255), default="primary")  # Google Calendar ID
    
    # Event details
    title = Column(String(255), nullable=False)
    description = Column(Text)
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=False)
    
    # Status tracking
    sync_status = Column(String(50), default="pending")  # pending, synced, failed, deleted
    last_sync_at = Column(DateTime(timezone=True))
    sync_error = Column(Text)  # Error message if sync failed
    
    # Event metadata
    event_link = Column(String(500))  # Google Calendar event URL
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    task = relationship("Task", back_populates="calendar_events")
    user = relationship("User")
    
    def __repr__(self):
        return f"<CalendarEvent(id={self.id}, title='{self.title}', sync_status='{self.sync_status}')>"

class DailyPlan(Base):
    """
    DailyPlan model for storing generated daily plans.
    
    This model stores the complete daily plans generated by the agentic workflow,
    enabling plan history tracking and analysis.
    """
    __tablename__ = "daily_plans"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), ForeignKey("users.user_id"), nullable=False)
    
    # Plan details
    date = Column(String(20), nullable=False)  # YYYY-MM-DD format
    goal_ids = Column(JSON)  # List of goal IDs included in this plan
    
    # Plan content
    tasks_data = Column(JSON)  # Complete task information
    time_blocks = Column(JSON)  # Time blocking information
    expert_advice = Column(JSON)  # Advice from all specialist agents
    
    # Plan metadata
    total_estimated_minutes = Column(Integer)
    available_hours = Column(Float)
    preferences_used = Column(JSON)  # User preferences applied to this plan
    
    # Execution tracking
    execution_status = Column(String(50), default="planned")  # planned, in_progress, completed
    actual_completion_rate = Column(Float)  # Percentage of tasks actually completed
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<DailyPlan(id={self.id}, date='{self.date}', status='{self.execution_status}')>"

# Utility functions for model operations
def create_sample_data():
    """
    Create sample data for testing and development.
    
    This function creates sample users, goals, and tasks for testing
    the application functionality.
    """
    from app.db.database import get_db_session
    
    session = get_db_session()
    try:
        # Create sample user
        sample_user = User(
            user_id="user_123",
            email="<EMAIL>",
            name="John Doe",
            timezone="America/New_York",
            preferences={
                "difficulty_preference": 0.7,
                "category_order": ["fitness", "learning", "career"],
                "preferred_session_length": 45
            }
        )
        session.add(sample_user)
        session.flush()  # Get the ID
        
        # Create sample goals
        fitness_goal = Goal(
            user_id="user_123",
            title="Get in shape for summer",
            description="Improve overall fitness and lose 15 pounds",
            category="fitness",
            priority=5,
            target_date="2024-06-01"
        )
        
        learning_goal = Goal(
            user_id="user_123",
            title="Learn Python programming",
            description="Master Python for data science applications",
            category="learning",
            priority=4,
            target_date="2024-05-01"
        )
        
        session.add_all([fitness_goal, learning_goal])
        session.commit()
        
        print("Sample data created successfully")
        
    except Exception as e:
        session.rollback()
        print(f"Error creating sample data: {e}")
        raise
    finally:
        session.close()
