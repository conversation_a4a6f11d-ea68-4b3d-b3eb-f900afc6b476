"""
Simplified workflow for Personal Growth Architect
This provides a working implementation without complex LangGraph dependencies
"""

from typing import Dict, Any, List
import json
import os
from datetime import datetime, timedelta

class SimplePersonalGrowthWorkflow:
    """
    Simplified workflow that generates daily plans without LangGraph complexity.
    This ensures the API works even if LangGraph/LangChain setup has issues.
    """
    
    def __init__(self):
        self.openrouter_key = os.getenv("OPENROUTER_API_KEY")
        self.has_ai = bool(self.openrouter_key)
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the workflow to generate a daily plan.

        Args:
            input_data: Dictionary containing user_id, goals, date, etc.

        Returns:
            Dictionary containing the generated daily plan
        """
        try:
            print(f"DEBUG: Workflow execute called with input: {input_data}")
            goals = input_data.get("goals", [])
            available_hours = input_data.get("available_hours", 2.0)
            date = input_data.get("date", datetime.now().strftime("%Y-%m-%d"))
            preferences = input_data.get("preferences", {})

            print(f"DEBUG: Parsed - goals: {goals}, hours: {available_hours}, date: {date}")
            
            if not goals:
                return {
                    "tasks": [],
                    "calendar_events": [],
                    "expert_advice": "No goals provided for planning.",
                    "estimated_duration": 0.0,
                    "time_blocks": []
                }
            
            # Generate tasks based on goals
            print(f"DEBUG: Starting task generation for {len(goals)} goals")
            tasks = self._generate_tasks(goals, available_hours)
            print(f"DEBUG: Generated {len(tasks)} tasks")

            # Generate expert advice
            print("DEBUG: Generating expert advice")
            expert_advice = self._generate_expert_advice(goals, tasks)
            print(f"DEBUG: Expert advice length: {len(expert_advice)}")

            # Create time blocks
            print("DEBUG: Creating time blocks")
            time_blocks = self._create_time_blocks(tasks, available_hours)
            print(f"DEBUG: Created {len(time_blocks)} time blocks")

            # Calculate total duration
            total_duration = sum(task.get("estimated_minutes", 0) for task in tasks) / 60.0
            print(f"DEBUG: Total duration: {total_duration} hours")
            
            return {
                "tasks": tasks,
                "calendar_events": [],  # Calendar integration can be added later
                "expert_advice": expert_advice,
                "estimated_duration": min(total_duration, available_hours),
                "time_blocks": time_blocks
            }
            
        except Exception as e:
            import traceback
            print(f"Workflow execution error: {e}")
            print(f"Full traceback: {traceback.format_exc()}")
            return {
                "error": f"Workflow execution failed: {str(e)}",
                "tasks": [],
                "calendar_events": [],
                "expert_advice": f"Error generating plan: {str(e)}",
                "estimated_duration": 0.0,
                "time_blocks": []
            }
    
    def _generate_tasks(self, goals: List[Dict[str, Any]], available_hours: float) -> List[Dict[str, Any]]:
        """Generate tasks based on goals and available time."""
        try:
            print(f"DEBUG: Generating tasks for {len(goals)} goals, {available_hours} hours")
            tasks = []
            total_minutes = available_hours * 60
            minutes_per_goal = total_minutes / len(goals) if goals else 0

            for i, goal in enumerate(goals):
                print(f"DEBUG: Processing goal {i}: {goal}")
                category = goal.get("category", "general")
                title = goal.get("title", f"Goal {i+1}")
                description = goal.get("description", "")

                # Generate category-specific tasks
                generated_tasks = None
                if category.lower() in ["fitness", "health"]:
                    generated_tasks = self._generate_fitness_tasks(title, description, minutes_per_goal)
                elif category.lower() in ["learning", "skill", "education"]:
                    generated_tasks = self._generate_learning_tasks(title, description, minutes_per_goal)
                elif category.lower() in ["career", "professional"]:
                    generated_tasks = self._generate_career_tasks(title, description, minutes_per_goal)
                else:
                    generated_tasks = self._generate_general_tasks(title, description, minutes_per_goal)

                print(f"DEBUG: Generated {len(generated_tasks) if generated_tasks else 0} tasks for category {category}")

                if generated_tasks:
                    tasks.extend(generated_tasks)
                else:
                    print(f"WARNING: No tasks generated for goal {title}")

            print(f"DEBUG: Total tasks generated: {len(tasks)}")
            return tasks[:10]  # Limit to 10 tasks max

        except Exception as e:
            print(f"ERROR in _generate_tasks: {e}")
            return []
    
    def _generate_fitness_tasks(self, title: str, description: str, available_minutes: float) -> List[Dict[str, Any]]:
        """Generate fitness-related tasks."""
        base_tasks = [
            {
                "name": f"Workout session for: {title}",
                "description": f"Focus on exercises related to {description}",
                "estimated_minutes": min(45, int(available_minutes * 0.7)),
                "difficulty": 4,
                "category": "fitness",
                "type": "exercise"
            },
            {
                "name": "Meal planning",
                "description": "Plan healthy meals to support your fitness goals",
                "estimated_minutes": min(15, int(available_minutes * 0.3)),
                "difficulty": 2,
                "category": "fitness",
                "type": "nutrition"
            }
        ]
        return [task for task in base_tasks if task["estimated_minutes"] > 0]
    
    def _generate_learning_tasks(self, title: str, description: str, available_minutes: float) -> List[Dict[str, Any]]:
        """Generate learning-related tasks."""
        base_tasks = [
            {
                "name": f"Study session: {title}",
                "description": f"Focused learning time for {description}",
                "estimated_minutes": min(60, int(available_minutes * 0.8)),
                "difficulty": 3,
                "category": "learning",
                "type": "study"
            },
            {
                "name": "Practice exercises",
                "description": f"Hands-on practice related to {title}",
                "estimated_minutes": min(30, int(available_minutes * 0.2)),
                "difficulty": 4,
                "category": "learning",
                "type": "practice"
            }
        ]
        return [task for task in base_tasks if task["estimated_minutes"] > 0]
    
    def _generate_career_tasks(self, title: str, description: str, available_minutes: float) -> List[Dict[str, Any]]:
        """Generate career-related tasks."""
        base_tasks = [
            {
                "name": f"Professional development: {title}",
                "description": f"Work on {description}",
                "estimated_minutes": min(90, int(available_minutes * 0.6)),
                "difficulty": 3,
                "category": "career",
                "type": "development"
            },
            {
                "name": "Networking activity",
                "description": "Connect with professionals in your field",
                "estimated_minutes": min(30, int(available_minutes * 0.4)),
                "difficulty": 2,
                "category": "career",
                "type": "networking"
            }
        ]
        return [task for task in base_tasks if task["estimated_minutes"] > 0]
    
    def _generate_general_tasks(self, title: str, description: str, available_minutes: float) -> List[Dict[str, Any]]:
        """Generate general tasks."""
        return [{
            "name": f"Work on: {title}",
            "description": description or f"Make progress on {title}",
            "estimated_minutes": min(60, int(available_minutes)),
            "difficulty": 3,
            "category": "general",
            "type": "general"
        }]
    
    def _generate_expert_advice(self, goals: List[Dict[str, Any]], tasks: List[Dict[str, Any]]) -> str:
        """Generate expert advice based on goals and tasks."""
        advice_parts = []
        
        # Analyze goal categories
        categories = set(goal.get("category", "general") for goal in goals)
        
        if "fitness" in categories:
            advice_parts.append("**Fitness Coach**: Start with a proper warm-up, focus on form over intensity, and remember to stay hydrated. Consistency is key to achieving your fitness goals.")
        
        if "learning" in categories:
            advice_parts.append("**Learning Coach**: Use active learning techniques like summarizing and teaching concepts back to yourself. Take breaks every 25-30 minutes to maintain focus.")
        
        if "career" in categories:
            advice_parts.append("**Career Coach**: Set specific, measurable objectives for each session. Document your progress and achievements for future reference in performance reviews.")
        
        if not advice_parts:
            advice_parts.append("**General Advice**: Break down your tasks into smaller, manageable chunks. Celebrate small wins to maintain motivation throughout your journey.")
        
        # Add time management advice
        total_time = sum(task.get("estimated_minutes", 0) for task in tasks) / 60.0
        if total_time > 0:
            advice_parts.append(f"**Time Management**: You have approximately {total_time:.1f} hours of planned activities. Consider scheduling short breaks between tasks to maintain energy and focus.")
        
        return "\n\n".join(advice_parts)
    
    def _create_time_blocks(self, tasks: List[Dict[str, Any]], available_hours: float) -> List[Dict[str, Any]]:
        """Create time blocks for the tasks."""
        time_blocks = []
        current_minute = 0
        total_minutes = available_hours * 60
        
        for i, task in enumerate(tasks):
            duration = task.get("estimated_minutes", 30)
            if current_minute + duration <= total_minutes:
                time_blocks.append({
                    "task_id": i,
                    "task_name": task.get("name"),
                    "start_minute": current_minute,
                    "duration_minutes": duration,
                    "category": task.get("category", "general")
                })
                current_minute += duration + 5  # 5-minute buffer
        
        return time_blocks
    
    async def process_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process user feedback for future improvements."""
        try:
            difficulty_rating = feedback_data.get("difficulty_rating", 3)
            satisfaction_rating = feedback_data.get("satisfaction_rating", 3)
            completed_tasks = feedback_data.get("completed_tasks", [])
            
            adaptations = []
            
            if difficulty_rating >= 4:
                adaptations.append("Consider reducing task difficulty or duration in future plans")
            elif difficulty_rating <= 2:
                adaptations.append("Consider increasing task complexity or adding more challenging activities")
            
            if satisfaction_rating <= 2:
                adaptations.append("Review goal alignment and task relevance")
            elif satisfaction_rating >= 4:
                adaptations.append("Maintain current approach - user is satisfied with the plan structure")
            
            completion_rate = len(completed_tasks) / max(1, len(feedback_data.get("planned_tasks", [])))
            if completion_rate < 0.5:
                adaptations.append("Consider reducing the number of tasks or their duration")
            elif completion_rate > 0.8:
                adaptations.append("User is completing tasks well - consider adding more activities")
            
            recommendations = f"Based on your feedback (difficulty: {difficulty_rating}/5, satisfaction: {satisfaction_rating}/5), we'll adjust future plans to better match your preferences and capacity."
            
            return {
                "adaptations": adaptations,
                "recommendations": recommendations
            }
            
        except Exception as e:
            return {
                "adaptations": ["Error processing feedback"],
                "recommendations": f"Feedback processing failed: {str(e)}"
            }
