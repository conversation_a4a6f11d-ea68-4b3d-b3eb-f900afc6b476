"""
Personal Growth Architect - External Tools Integration

This module contains functions for integrating with external tools and services,
primarily Google Calendar API for scheduling and time management.

Tools included:
- Google Calendar integration for event creation and management
- Time zone handling utilities
- Calendar conflict detection
- Event formatting and validation
"""

import os
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json

# Google Calendar API imports
try:
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import Flow
    from google.auth.transport.requests import Request
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    print("Google Calendar API not available. Install google-api-python-client to enable calendar integration.")

# LangChain tool imports
from langchain.tools import tool

def get_tools() -> List:
    """
    Return a list of available tools for the LangGraph workflow.
    
    These tools can be used by agents to interact with external services
    and perform actions beyond text generation.
    """
    tools = []
    
    if GOOGLE_AVAILABLE:
        tools.extend([
            update_google_calendar,
            get_calendar_events,
            check_calendar_conflicts
        ])
    
    tools.extend([
        format_time_blocks,
        calculate_optimal_schedule
    ])
    
    return tools

@tool
def update_google_calendar(
    event_title: str,
    start_time: str,
    duration_minutes: int,
    description: str = "",
    calendar_id: str = "primary"
) -> Dict[str, Any]:
    """
    Create or update an event in Google Calendar.
    
    Args:
        event_title: Title of the calendar event
        start_time: Start time in ISO format (e.g., "2024-01-15T09:00:00")
        duration_minutes: Duration of the event in minutes
        description: Optional event description
        calendar_id: Google Calendar ID (default: "primary")
    
    Returns:
        Dictionary with event creation status and event details
    """
    if not GOOGLE_AVAILABLE:
        return {
            "success": False,
            "error": "Google Calendar API not available",
            "event_id": None
        }
    
    try:
        # Initialize Google Calendar service
        service = _get_calendar_service()
        if not service:
            return {
                "success": False,
                "error": "Failed to authenticate with Google Calendar",
                "event_id": None
            }
        
        # Parse start time and calculate end time
        start_datetime = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_datetime = start_datetime + timedelta(minutes=duration_minutes)
        
        # Create event object
        event = {
            'summary': event_title,
            'description': description,
            'start': {
                'dateTime': start_datetime.isoformat(),
                'timeZone': 'UTC',  # TODO: Make timezone configurable
            },
            'end': {
                'dateTime': end_datetime.isoformat(),
                'timeZone': 'UTC',
            },
            'reminders': {
                'useDefault': False,
                'overrides': [
                    {'method': 'popup', 'minutes': 10},
                ],
            },
        }
        
        # Insert event into calendar
        created_event = service.events().insert(
            calendarId=calendar_id,
            body=event
        ).execute()
        
        return {
            "success": True,
            "event_id": created_event.get('id'),
            "event_link": created_event.get('htmlLink'),
            "start_time": start_time,
            "duration_minutes": duration_minutes
        }
        
    except HttpError as error:
        return {
            "success": False,
            "error": f"Google Calendar API error: {error}",
            "event_id": None
        }
    except Exception as error:
        return {
            "success": False,
            "error": f"Calendar update failed: {str(error)}",
            "event_id": None
        }

@tool
def get_calendar_events(
    start_date: str,
    end_date: str,
    calendar_id: str = "primary"
) -> Dict[str, Any]:
    """
    Retrieve events from Google Calendar for a specified date range.
    
    Args:
        start_date: Start date in ISO format
        end_date: End date in ISO format
        calendar_id: Google Calendar ID (default: "primary")
    
    Returns:
        Dictionary with list of events and metadata
    """
    if not GOOGLE_AVAILABLE:
        return {
            "success": False,
            "error": "Google Calendar API not available",
            "events": []
        }
    
    try:
        service = _get_calendar_service()
        if not service:
            return {
                "success": False,
                "error": "Failed to authenticate with Google Calendar",
                "events": []
            }
        
        # Call the Calendar API
        events_result = service.events().list(
            calendarId=calendar_id,
            timeMin=start_date,
            timeMax=end_date,
            singleEvents=True,
            orderBy='startTime'
        ).execute()
        
        events = events_result.get('items', [])
        
        # Format events for easier consumption
        formatted_events = []
        for event in events:
            start = event['start'].get('dateTime', event['start'].get('date'))
            end = event['end'].get('dateTime', event['end'].get('date'))
            
            formatted_events.append({
                'id': event.get('id'),
                'title': event.get('summary', 'No Title'),
                'start_time': start,
                'end_time': end,
                'description': event.get('description', ''),
                'location': event.get('location', '')
            })
        
        return {
            "success": True,
            "events": formatted_events,
            "count": len(formatted_events)
        }
        
    except HttpError as error:
        return {
            "success": False,
            "error": f"Google Calendar API error: {error}",
            "events": []
        }
    except Exception as error:
        return {
            "success": False,
            "error": f"Failed to retrieve events: {str(error)}",
            "events": []
        }

@tool
def check_calendar_conflicts(
    proposed_events: List[Dict[str, Any]],
    calendar_id: str = "primary"
) -> Dict[str, Any]:
    """
    Check for conflicts between proposed events and existing calendar events.
    
    Args:
        proposed_events: List of proposed events with start_time and duration
        calendar_id: Google Calendar ID to check against
    
    Returns:
        Dictionary with conflict analysis and suggestions
    """
    try:
        # Get existing events for the relevant time period
        if proposed_events:
            # Find the date range to check
            start_times = [event.get('start_time') for event in proposed_events if event.get('start_time')]
            if start_times:
                earliest = min(start_times)
                latest = max(start_times)
                
                # Add buffer for duration
                start_date = earliest
                end_date = (datetime.fromisoformat(latest.replace('Z', '+00:00')) + timedelta(hours=3)).isoformat()
                
                existing_events = get_calendar_events(start_date, end_date, calendar_id)
                
                if not existing_events.get('success'):
                    return {
                        "success": False,
                        "error": "Could not retrieve existing events",
                        "conflicts": []
                    }
                
                # Check for conflicts
                conflicts = []
                for proposed in proposed_events:
                    prop_start = datetime.fromisoformat(proposed['start_time'].replace('Z', '+00:00'))
                    prop_end = prop_start + timedelta(minutes=proposed.get('duration_minutes', 30))
                    
                    for existing in existing_events.get('events', []):
                        exist_start = datetime.fromisoformat(existing['start_time'].replace('Z', '+00:00'))
                        exist_end = datetime.fromisoformat(existing['end_time'].replace('Z', '+00:00'))
                        
                        # Check for overlap
                        if (prop_start < exist_end and prop_end > exist_start):
                            conflicts.append({
                                "proposed_event": proposed.get('title', 'Unnamed'),
                                "conflicting_event": existing.get('title'),
                                "conflict_time": existing['start_time'],
                                "suggestion": f"Consider rescheduling to avoid conflict with {existing.get('title')}"
                            })
                
                return {
                    "success": True,
                    "conflicts": conflicts,
                    "has_conflicts": len(conflicts) > 0
                }
        
        return {
            "success": True,
            "conflicts": [],
            "has_conflicts": False
        }
        
    except Exception as error:
        return {
            "success": False,
            "error": f"Conflict check failed: {str(error)}",
            "conflicts": []
        }

@tool
def format_time_blocks(
    tasks: List[Dict[str, Any]],
    start_time: str,
    available_hours: float
) -> Dict[str, Any]:
    """
    Format tasks into time blocks with specific start and end times.
    
    Args:
        tasks: List of tasks with duration estimates
        start_time: Preferred start time in ISO format
        available_hours: Total available hours for scheduling
    
    Returns:
        Dictionary with formatted time blocks
    """
    try:
        start_datetime = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        current_time = start_datetime
        time_blocks = []
        total_minutes = available_hours * 60
        used_minutes = 0
        
        for i, task in enumerate(tasks):
            duration = task.get('estimated_minutes', 30)
            
            if used_minutes + duration <= total_minutes:
                end_time = current_time + timedelta(minutes=duration)
                
                time_blocks.append({
                    "task_id": task.get('id', i),
                    "task_name": task.get('name', f'Task {i+1}'),
                    "start_time": current_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_minutes": duration,
                    "category": task.get('category', 'general'),
                    "description": task.get('description', ''),
                    "calendar_ready": True
                })
                
                current_time = end_time + timedelta(minutes=5)  # 5-minute buffer
                used_minutes += duration + 5
        
        return {
            "success": True,
            "time_blocks": time_blocks,
            "total_scheduled_minutes": used_minutes - (len(time_blocks) * 5),  # Remove buffer from total
            "remaining_minutes": total_minutes - used_minutes
        }
        
    except Exception as error:
        return {
            "success": False,
            "error": f"Time block formatting failed: {str(error)}",
            "time_blocks": []
        }

@tool
def calculate_optimal_schedule(
    tasks: List[Dict[str, Any]],
    preferences: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Calculate optimal scheduling based on task priorities and user preferences.
    
    Args:
        tasks: List of tasks to schedule
        preferences: User preferences for scheduling
    
    Returns:
        Dictionary with optimized task order and timing suggestions
    """
    try:
        # Simple optimization algorithm
        # In a real implementation, this could use more sophisticated algorithms
        
        # Priority factors
        difficulty_weight = preferences.get('difficulty_preference', 0.5)  # 0 = easy first, 1 = hard first
        category_preferences = preferences.get('category_order', [])
        
        # Score each task
        scored_tasks = []
        for task in tasks:
            score = 0
            
            # Difficulty scoring
            difficulty = task.get('difficulty', 3)
            if difficulty_weight > 0.5:
                score += difficulty * 10  # Hard tasks first
            else:
                score += (6 - difficulty) * 10  # Easy tasks first
            
            # Category preference scoring
            category = task.get('category', 'general')
            if category in category_preferences:
                score += (len(category_preferences) - category_preferences.index(category)) * 5
            
            scored_tasks.append({
                **task,
                'optimization_score': score
            })
        
        # Sort by score
        optimized_tasks = sorted(scored_tasks, key=lambda x: x['optimization_score'], reverse=True)
        
        return {
            "success": True,
            "optimized_tasks": optimized_tasks,
            "optimization_notes": [
                f"Tasks ordered by difficulty preference: {'Hard first' if difficulty_weight > 0.5 else 'Easy first'}",
                f"Category preferences applied: {category_preferences}"
            ]
        }
        
    except Exception as error:
        return {
            "success": False,
            "error": f"Schedule optimization failed: {str(error)}",
            "optimized_tasks": tasks
        }

def _get_calendar_service():
    """
    Initialize and return Google Calendar service object.
    
    This function handles OAuth2 authentication and returns a service object
    for interacting with the Google Calendar API.
    
    Note: In a production environment, you would implement proper OAuth2 flow
    with token storage and refresh logic.
    """
    if not GOOGLE_AVAILABLE:
        return None
    
    try:
        # TODO: Implement proper OAuth2 flow
        # This is a placeholder that would need actual credential handling
        
        # For now, return None to indicate authentication is needed
        # In a real implementation, you would:
        # 1. Check for stored credentials
        # 2. Refresh expired tokens
        # 3. Initiate OAuth flow if needed
        # 4. Return authenticated service object
        
        return None
        
    except Exception as error:
        print(f"Calendar service initialization failed: {error}")
        return None
