"""
Personal Growth Architect - Database Configuration

This module contains SQLAlchemy database setup and configuration for
the Personal Growth Architect application. It handles:

- Database engine creation and configuration
- Session management
- Database initialization
- Connection utilities
"""

import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./personal_growth.db")

# Create SQLAlchemy engine
# For SQLite, we use StaticPool to handle threading issues
if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=os.getenv("DEBUG", "False").lower() == "true"  # Enable SQL logging in debug mode
    )
else:
    # For other databases (PostgreSQL, MySQL, etc.)
    engine = create_engine(
        DATABASE_URL,
        echo=os.getenv("DEBUG", "False").lower() == "true"
    )

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for declarative models
Base = declarative_base()

def get_db() -> Session:
    """
    Dependency function to get database session.
    
    This function creates a new database session for each request
    and ensures it's properly closed after use.
    
    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """
    Initialize the database by creating all tables.
    
    This function should be called on application startup to ensure
    all required tables exist in the database.
    """
    try:
        # Import all models to ensure they're registered with Base
        from app.db.models import User, Goal, Task, Feedback, CalendarEvent
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("Database initialized successfully")
        
    except Exception as e:
        print(f"Database initialization failed: {e}")
        raise

def get_db_session() -> Session:
    """
    Create and return a new database session.
    
    Use this function when you need a database session outside of
    the FastAPI dependency injection system.
    
    Returns:
        Session: New SQLAlchemy database session
        
    Note:
        Remember to close the session when done:
        session = get_db_session()
        try:
            # Use session
            pass
        finally:
            session.close()
    """
    return SessionLocal()

def close_db_connections():
    """
    Close all database connections.
    
    This function should be called during application shutdown
    to ensure all database connections are properly closed.
    """
    try:
        engine.dispose()
        print("Database connections closed successfully")
    except Exception as e:
        print(f"Error closing database connections: {e}")

# Database health check function
def check_db_health() -> bool:
    """
    Check if the database is accessible and healthy.
    
    Returns:
        bool: True if database is accessible, False otherwise
    """
    try:
        # Try to create a session and execute a simple query
        session = get_db_session()
        try:
            # Execute a simple query to test connection
            session.execute("SELECT 1")
            return True
        finally:
            session.close()
    except Exception as e:
        print(f"Database health check failed: {e}")
        return False

# Context manager for database sessions
class DatabaseSession:
    """
    Context manager for database sessions.
    
    Usage:
        with DatabaseSession() as session:
            # Use session here
            user = session.query(User).first()
    """
    
    def __init__(self):
        self.session = None
    
    def __enter__(self) -> Session:
        self.session = get_db_session()
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            if exc_type is not None:
                # Rollback on exception
                self.session.rollback()
            self.session.close()

# Database utility functions
def create_tables():
    """Create all database tables."""
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """Drop all database tables. Use with caution!"""
    Base.metadata.drop_all(bind=engine)

def reset_database():
    """
    Reset the database by dropping and recreating all tables.
    
    WARNING: This will delete all data in the database!
    Only use this for development/testing purposes.
    """
    print("WARNING: Resetting database - all data will be lost!")
    drop_tables()
    create_tables()
    print("Database reset completed")

# Migration utilities (basic)
def backup_database(backup_path: str = None):
    """
    Create a backup of the SQLite database.
    
    Args:
        backup_path: Path for the backup file. If None, uses timestamp.
    
    Note:
        This is a simple backup function for SQLite only.
        For production use, consider proper backup strategies.
    """
    if not DATABASE_URL.startswith("sqlite"):
        raise NotImplementedError("Backup only supported for SQLite databases")
    
    import shutil
    from datetime import datetime
    
    if backup_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"backup_personal_growth_{timestamp}.db"
    
    try:
        # Extract database file path from URL
        db_file = DATABASE_URL.replace("sqlite:///", "")
        shutil.copy2(db_file, backup_path)
        print(f"Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"Backup failed: {e}")
        raise
