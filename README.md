# Personal Growth Architect

An AI-powered life coaching system that helps users achieve their personal growth goals through intelligent task decomposition, expert guidance, and automated scheduling.

## Project Overview

The Personal Growth Architect is an agentic AI system that acts as a comprehensive life coach. It takes high-level personal growth goals and transforms them into actionable, scheduled tasks with expert guidance. The system uses a multi-agent architecture to provide specialized advice across different domains (fitness, learning, career development) and integrates with Google Calendar for seamless scheduling.

### Key Features

- **Goal Clarification**: Transforms vague goals into specific, measurable objectives
- **Task Decomposition**: Breaks down goals into actionable daily tasks
- **Expert Guidance**: Provides specialized advice from virtual coaches (Health, Learning, Career)
- **Intelligent Scheduling**: Creates optimized time blocks and calendar integration
- **Adaptive Planning**: Learns from user feedback to improve future recommendations
- **Calendar Integration**: Automatically schedules tasks in Google Calendar

## Architecture Explanation

The system follows a hierarchical, multi-agent pattern orchestrated by LangGraph:

### Core Components

1. **FastAPI Backend** (`app/main.py`)
   - Serves REST API endpoints for goal creation, plan generation, and feedback submission
   - Handles HTTP requests and responses
   - Manages database interactions
   - Triggers the agentic workflow

2. **LangGraph Workflow** (`app/graph.py`)
   - Orchestrates the multi-agent system
   - Manages state flow between agents
   - Handles conditional routing based on goal categories
   - Coordinates parallel agent execution

3. **Specialist Agents** (`app/agents.py`)
   - **Goal_Clarifier**: Refines and clarifies user goals
   - **Task_Decomposer**: Breaks goals into actionable tasks
   - **Health_Coach**: Provides fitness and wellness expertise
   - **Learning_Coach**: Offers skill development guidance
   - **Career_Coach**: Delivers professional development advice
   - **Time_Strategist**: Optimizes scheduling and time management
   - **Daily_Planner**: Assembles the final daily plan
   - **Feedback_Analyst**: Processes feedback for plan adaptation

4. **External Tools** (`app/tools.py`)
   - Google Calendar API integration
   - Time blocking and scheduling utilities
   - Conflict detection and resolution

5. **Database Layer** (`app/db/`)
   - SQLAlchemy ORM models for data persistence
   - User goals, tasks, feedback, and calendar events storage
   - SQLite database for development (easily configurable for production)

### Workflow Process

1. **Input**: User submits a high-level goal (e.g., "get fit")
2. **Clarification**: Goal_Clarifier makes the goal specific and measurable
3. **Decomposition**: Task_Decomposer breaks it into daily actionable tasks
4. **Expert Advice**: Specialist coaches provide domain-specific guidance
5. **Scheduling**: Time_Strategist creates optimized time blocks
6. **Integration**: Calendar events are created in Google Calendar
7. **Delivery**: Complete daily plan is returned to the user
8. **Feedback Loop**: User feedback is processed to improve future plans

## Setup Instructions

### Prerequisites

- Python 3.9 or higher
- `uv` package manager (recommended) or `pip`
- Google Cloud Console account (for Calendar API)
- OpenRouter API account

### Installation Steps

#### Using uv (Recommended)

1. **Install uv** (if not already installed):
   ```bash
   # On Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   
   # On macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Clone and navigate to the project**:
   ```bash
   cd personal-growth-architect
   ```

3. **Create virtual environment and install dependencies**:
   ```bash
   uv venv
   uv pip install -r requirements.txt
   ```

4. **Activate the virtual environment**:
   ```bash
   # On Windows
   .venv\Scripts\activate
   
   # On macOS/Linux
   source .venv/bin/activate
   ```

#### Using pip (Alternative)

1. **Create virtual environment**:
   ```bash
   python -m venv venv
   
   # Activate it
   # Windows:
   venv\Scripts\activate
   # macOS/Linux:
   source venv/bin/activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Configuration

1. **Copy environment file**:
   ```bash
   cp .env.example .env
   ```

2. **Configure environment variables** in `.env`:
   ```env
   # OpenRouter API (required)
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   
   # Google Calendar API (optional, for calendar integration)
   GOOGLE_CLIENT_ID=your_google_client_id_here
   GOOGLE_CLIENT_SECRET=your_google_client_secret_here
   
   # Database (SQLite by default)
   DATABASE_URL=sqlite:///./personal_growth.db
   
   # Application settings
   SECRET_KEY=your_secret_key_here
   DEBUG=True
   ```

3. **Set up Google Calendar API** (optional):
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable the Google Calendar API
   - Create credentials (OAuth 2.0 Client ID)
   - Add your credentials to the `.env` file

### Running the Application

1. **Initialize the database**:
   ```bash
   python -c "from app.db.database import init_db; init_db()"
   ```

2. **Start the FastAPI server**:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Access the application**:
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/

### API Usage Examples

#### Create a Goal
```bash
curl -X POST "http://localhost:8000/create_goal" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_123",
    "title": "Get in shape",
    "description": "Improve fitness and lose weight",
    "category": "fitness",
    "priority": 5
  }'
```

#### Generate Daily Plan
```bash
curl -X POST "http://localhost:8000/get_daily_plan" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_123",
    "goal_ids": [1],
    "date": "2024-01-15",
    "available_hours": 2.0
  }'
```

#### Submit Feedback
```bash
curl -X POST "http://localhost:8000/submit_feedback" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_123",
    "date": "2024-01-15",
    "completed_tasks": ["Morning workout", "Meal prep"],
    "difficulty_rating": 3,
    "satisfaction_rating": 4,
    "notes": "Great workout, but meal prep took longer than expected"
  }'
```

## Development

### Project Structure
```
personal-growth-architect/
├── app/
│   ├── __init__.py
│   ├── main.py           # FastAPI application and endpoints
│   ├── graph.py          # LangGraph workflow definition
│   ├── agents.py         # Agent node implementations
│   ├── tools.py          # External tool integrations
│   └── db/
│       ├── __init__.py
│       ├── database.py   # SQLAlchemy configuration
│       └── models.py     # ORM models
├── .env.example          # Environment variables template
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

### Testing

Run tests with pytest:
```bash
pytest
```

### Code Quality

Format code with black:
```bash
black app/
```

Lint with flake8:
```bash
flake8 app/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions or issues, please open an issue on the GitHub repository or contact the development team.
