"""
Personal Growth Architect - LangGraph Workflow Definition

This module defines the LangGraph state and workflow that orchestrates
the multi-agent system for personal growth planning.

The workflow follows this sequence:
1. Goal Clarification -> Task Decomposition
2. Parallel execution of specialist agents (Health Coach, etc.)
3. Time Strategy and Calendar Integration
4. Daily Plan Assembly
5. Feedback Processing (separate workflow)
"""

from typing import TypedDict, List, Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
import asyncio

from app.agents import (
    goal_clarifier_node,
    task_decomposer_node,
    health_coach_node,
    learning_coach_node,
    career_coach_node,
    time_strategist_node,
    daily_planner_node,
    feedback_analyst_node
)
from app.tools import get_tools

class PersonalGrowthState(TypedDict):
    """
    State object that flows through the LangGraph workflow.
    Each agent node can read from and update this state.
    """
    # Input data
    user_id: str
    goals: List[Dict[str, Any]]
    date: str
    available_hours: float
    preferences: Dict[str, Any]
    
    # Intermediate processing results
    clarified_goals: Optional[List[Dict[str, Any]]]
    decomposed_tasks: Optional[List[Dict[str, Any]]]
    expert_advice: Optional[Dict[str, str]]  # advice from different coaches
    time_blocks: Optional[List[Dict[str, Any]]]
    calendar_events: Optional[List[Dict[str, Any]]]
    
    # Final output
    daily_plan: Optional[Dict[str, Any]]
    
    # Feedback processing
    feedback_data: Optional[Dict[str, Any]]
    adaptations: Optional[List[str]]
    
    # Error handling
    errors: Optional[List[str]]

class PersonalGrowthWorkflow:
    """
    Main workflow orchestrator for the Personal Growth Architect system.
    
    This class manages the LangGraph workflow that coordinates multiple
    specialist agents to create personalized daily plans.
    """
    
    def __init__(self):
        self.tools = get_tools()
        self.tool_executor = ToolExecutor(self.tools)
        self.workflow = self._build_workflow()
        self.app = self.workflow.compile()
    
    def _build_workflow(self) -> StateGraph:
        """
        Build the LangGraph workflow with agent nodes and conditional edges.
        
        Workflow Structure:
        START -> Goal Clarifier -> Task Decomposer -> [Parallel Coaches] -> 
        Time Strategist -> Daily Planner -> END
        """
        workflow = StateGraph(PersonalGrowthState)
        
        # Add agent nodes
        workflow.add_node("goal_clarifier", goal_clarifier_node)
        workflow.add_node("task_decomposer", task_decomposer_node)
        workflow.add_node("health_coach", health_coach_node)
        workflow.add_node("learning_coach", learning_coach_node)
        workflow.add_node("career_coach", career_coach_node)
        workflow.add_node("time_strategist", time_strategist_node)
        workflow.add_node("daily_planner", daily_planner_node)
        
        # Define the workflow sequence
        workflow.set_entry_point("goal_clarifier")
        
        # Sequential flow: clarification -> decomposition
        workflow.add_edge("goal_clarifier", "task_decomposer")
        
        # Conditional routing to appropriate coaches based on goal categories
        workflow.add_conditional_edges(
            "task_decomposer",
            self._route_to_coaches,
            {
                "health": "health_coach",
                "learning": "learning_coach", 
                "career": "career_coach",
                "multiple": "health_coach"  # Start with health for multiple categories
            }
        )
        
        # All coaches flow to time strategist
        workflow.add_edge("health_coach", "time_strategist")
        workflow.add_edge("learning_coach", "time_strategist")
        workflow.add_edge("career_coach", "time_strategist")
        
        # Final assembly
        workflow.add_edge("time_strategist", "daily_planner")
        workflow.add_edge("daily_planner", END)
        
        return workflow
    
    def _route_to_coaches(self, state: PersonalGrowthState) -> str:
        """
        Determine which coach(es) to route to based on goal categories.
        
        This function analyzes the goal categories and decides the routing logic.
        For multiple categories, it can trigger parallel execution.
        """
        if not state.get("clarified_goals"):
            return "health"  # Default fallback
        
        categories = set()
        for goal in state["clarified_goals"]:
            categories.add(goal.get("category", "general"))
        
        # Route based on primary category
        if "fitness" in categories or "health" in categories:
            return "health"
        elif "learning" in categories or "skill" in categories:
            return "learning"
        elif "career" in categories or "professional" in categories:
            return "career"
        elif len(categories) > 1:
            return "multiple"
        else:
            return "health"  # Default
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the main workflow to generate a daily plan.
        
        Args:
            input_data: Dictionary containing user_id, goals, date, etc.
            
        Returns:
            Dictionary containing the generated daily plan
        """
        try:
            # Initialize state
            initial_state = PersonalGrowthState(
                user_id=input_data["user_id"],
                goals=input_data["goals"],
                date=input_data["date"],
                available_hours=input_data.get("available_hours", 2.0),
                preferences=input_data.get("preferences", {}),
                clarified_goals=None,
                decomposed_tasks=None,
                expert_advice=None,
                time_blocks=None,
                calendar_events=None,
                daily_plan=None,
                feedback_data=None,
                adaptations=None,
                errors=[]
            )
            
            # Execute the workflow
            final_state = await self.app.ainvoke(initial_state)
            
            # Extract and return the results
            return {
                "tasks": final_state.get("decomposed_tasks", []),
                "calendar_events": final_state.get("calendar_events", []),
                "expert_advice": self._format_expert_advice(final_state.get("expert_advice", {})),
                "estimated_duration": sum(
                    task.get("estimated_minutes", 0) 
                    for task in final_state.get("decomposed_tasks", [])
                ) / 60.0,
                "time_blocks": final_state.get("time_blocks", [])
            }
            
        except Exception as e:
            return {
                "error": f"Workflow execution failed: {str(e)}",
                "tasks": [],
                "calendar_events": [],
                "expert_advice": "",
                "estimated_duration": 0.0
            }
    
    async def process_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process user feedback through the Feedback Analyst agent.
        
        This creates a separate workflow for feedback processing that can
        adapt future planning based on user performance and satisfaction.
        """
        try:
            # Create feedback processing workflow
            feedback_workflow = StateGraph(PersonalGrowthState)
            feedback_workflow.add_node("feedback_analyst", feedback_analyst_node)
            feedback_workflow.set_entry_point("feedback_analyst")
            feedback_workflow.add_edge("feedback_analyst", END)
            
            feedback_app = feedback_workflow.compile()
            
            # Initialize state for feedback processing
            feedback_state = PersonalGrowthState(
                user_id=feedback_data["user_id"],
                goals=[],  # Will be loaded by feedback analyst
                date=feedback_data["date"],
                available_hours=0.0,
                preferences={},
                clarified_goals=None,
                decomposed_tasks=None,
                expert_advice=None,
                time_blocks=None,
                calendar_events=None,
                daily_plan=None,
                feedback_data=feedback_data,
                adaptations=None,
                errors=[]
            )
            
            # Execute feedback analysis
            result_state = await feedback_app.ainvoke(feedback_state)
            
            return {
                "adaptations": result_state.get("adaptations", []),
                "recommendations": result_state.get("expert_advice", {}).get("feedback_analyst", "")
            }
            
        except Exception as e:
            return {
                "error": f"Feedback processing failed: {str(e)}",
                "adaptations": [],
                "recommendations": ""
            }
    
    def _format_expert_advice(self, advice_dict: Dict[str, str]) -> str:
        """Format expert advice from multiple coaches into a single string."""
        if not advice_dict:
            return "No expert advice available."
        
        formatted_advice = []
        for coach, advice in advice_dict.items():
            if advice:
                formatted_advice.append(f"**{coach.replace('_', ' ').title()}**: {advice}")
        
        return "\n\n".join(formatted_advice) if formatted_advice else "No expert advice available."
