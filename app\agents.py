"""
Personal Growth Architect - Agent Node Implementations

This module contains the implementation of each specialist agent node
that participates in the LangGraph workflow. Each agent has specific
expertise and contributes to the overall personal growth planning process.

Agent Roles:
- Goal_Clarifier: Refines and clarifies user goals
- Task_Decomposer: Breaks goals into actionable tasks
- Health_Coach: Provides fitness and wellness expertise
- Learning_Coach: Provides skill development and learning guidance
- Career_Coach: Provides professional development advice
- Time_Strategist: Optimizes scheduling and time management
- Daily_Planner: Assembles the final daily plan
- Feedback_Analyst: Processes feedback for plan adaptation
"""

from typing import Dict, Any, List
import os
from datetime import datetime, timedelta
import json

# Import LangChain components
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, SystemMessage

# Initialize LLM client
def get_llm():
    """Initialize and return the LLM client configured for OpenRouter."""
    return ChatOpenAI(
        model=os.getenv("DEFAULT_MODEL", "openai/gpt-4-turbo-preview"),
        openai_api_key=os.getenv("OPENROUTER_API_KEY"),
        openai_api_base=os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
        temperature=float(os.getenv("TEMPERATURE", "0.7")),
        max_tokens=int(os.getenv("MAX_TOKENS", "2000"))
    )

async def goal_clarifier_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Goal Clarifier Agent Node
    
    This agent takes high-level user goals and clarifies them by:
    - Making goals more specific and measurable
    - Identifying potential obstacles
    - Setting realistic timelines
    - Categorizing goals appropriately
    """
    try:
        llm = get_llm()
        
        goals = state.get("goals", [])
        user_preferences = state.get("preferences", {})
        
        # Create prompt for goal clarification
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a Goal Clarification specialist. Your role is to take high-level personal growth goals 
            and make them more specific, measurable, and actionable. 
            
            For each goal, provide:
            1. A clarified, specific version of the goal
            2. Success metrics or measurable outcomes
            3. Potential obstacles or challenges
            4. Recommended timeline
            5. Proper categorization (fitness, learning, career, personal, etc.)
            
            Return your response as a JSON array of clarified goals."""),
            ("human", f"""Please clarify these goals:
            Goals: {json.dumps(goals)}
            User Preferences: {json.dumps(user_preferences)}
            
            Make each goal SMART (Specific, Measurable, Achievable, Relevant, Time-bound).""")
        ])
        
        response = await llm.ainvoke(prompt.format_messages())
        
        # Parse the response (in a real implementation, add proper error handling)
        try:
            clarified_goals = json.loads(response.content)
        except json.JSONDecodeError:
            # Fallback: create basic clarified goals
            clarified_goals = [
                {
                    "id": goal.get("id"),
                    "original_title": goal.get("title"),
                    "clarified_title": goal.get("title"),
                    "description": goal.get("description"),
                    "category": goal.get("category", "general"),
                    "success_metrics": ["Progress tracking needed"],
                    "obstacles": ["Time management", "Consistency"],
                    "timeline": "4 weeks"
                }
                for goal in goals
            ]
        
        return {"clarified_goals": clarified_goals}
        
    except Exception as e:
        return {"errors": [f"Goal clarification failed: {str(e)}"]}

async def task_decomposer_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Task Decomposer Agent Node
    
    This agent breaks down clarified goals into specific, actionable tasks
    that can be scheduled and completed on a daily basis.
    """
    try:
        llm = get_llm()
        
        clarified_goals = state.get("clarified_goals", [])
        available_hours = state.get("available_hours", 2.0)
        date = state.get("date", datetime.now().strftime("%Y-%m-%d"))
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a Task Decomposition specialist. Break down goals into specific, 
            actionable tasks that can be completed in a single day or session.
            
            For each task, provide:
            1. Task name and description
            2. Estimated time in minutes
            3. Difficulty level (1-5)
            4. Prerequisites or dependencies
            5. Success criteria
            6. Category/goal association
            
            Return as JSON array of tasks."""),
            ("human", f"""Break down these goals into daily tasks:
            Goals: {json.dumps(clarified_goals)}
            Available time: {available_hours} hours
            Target date: {date}
            
            Focus on tasks that can be completed today and build momentum.""")
        ])
        
        response = await llm.ainvoke(prompt.format_messages())
        
        try:
            decomposed_tasks = json.loads(response.content)
        except json.JSONDecodeError:
            # Fallback task creation
            decomposed_tasks = []
            for goal in clarified_goals:
                decomposed_tasks.append({
                    "name": f"Work on: {goal.get('clarified_title', goal.get('title'))}",
                    "description": f"Make progress on {goal.get('description', '')}",
                    "estimated_minutes": 30,
                    "difficulty": 3,
                    "category": goal.get("category", "general"),
                    "goal_id": goal.get("id")
                })
        
        return {"decomposed_tasks": decomposed_tasks}
        
    except Exception as e:
        return {"errors": [f"Task decomposition failed: {str(e)}"]}

async def health_coach_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Health Coach Agent Node
    
    Provides expert advice for fitness, nutrition, and wellness-related goals.
    """
    try:
        llm = get_llm()
        
        tasks = state.get("decomposed_tasks", [])
        health_tasks = [task for task in tasks if task.get("category") in ["fitness", "health", "wellness"]]
        
        if not health_tasks:
            return {"expert_advice": {"health_coach": ""}}
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a certified Health and Fitness Coach. Provide expert advice for 
            health, fitness, and wellness tasks. Focus on:
            - Safe and effective exercise techniques
            - Proper progression and recovery
            - Nutrition guidance
            - Injury prevention
            - Motivation and habit formation
            
            Keep advice practical and actionable."""),
            ("human", f"""Provide expert coaching advice for these health-related tasks:
            {json.dumps(health_tasks)}
            
            Give specific, actionable advice for today's session.""")
        ])
        
        response = await llm.ainvoke(prompt.format_messages())
        
        current_advice = state.get("expert_advice", {})
        current_advice["health_coach"] = response.content
        
        return {"expert_advice": current_advice}
        
    except Exception as e:
        return {"errors": [f"Health coaching failed: {str(e)}"]}

async def learning_coach_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Learning Coach Agent Node
    
    Provides expert advice for skill development and learning-related goals.
    """
    try:
        llm = get_llm()
        
        tasks = state.get("decomposed_tasks", [])
        learning_tasks = [task for task in tasks if task.get("category") in ["learning", "skill", "education"]]
        
        if not learning_tasks:
            return {"expert_advice": {"learning_coach": ""}}
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are an expert Learning and Development Coach. Provide guidance for 
            skill acquisition and learning tasks. Focus on:
            - Effective learning strategies
            - Spaced repetition and practice
            - Resource recommendations
            - Progress tracking methods
            - Overcoming learning plateaus
            
            Tailor advice to adult learning principles."""),
            ("human", f"""Provide expert learning advice for these tasks:
            {json.dumps(learning_tasks)}
            
            Suggest specific learning techniques and resources for today.""")
        ])
        
        response = await llm.ainvoke(prompt.format_messages())
        
        current_advice = state.get("expert_advice", {})
        current_advice["learning_coach"] = response.content
        
        return {"expert_advice": current_advice}
        
    except Exception as e:
        return {"errors": [f"Learning coaching failed: {str(e)}"]}

async def career_coach_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Career Coach Agent Node
    
    Provides expert advice for professional development and career-related goals.
    """
    try:
        llm = get_llm()
        
        tasks = state.get("decomposed_tasks", [])
        career_tasks = [task for task in tasks if task.get("category") in ["career", "professional", "networking"]]
        
        if not career_tasks:
            return {"expert_advice": {"career_coach": ""}}
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a professional Career Development Coach. Provide strategic advice for 
            career advancement and professional growth. Focus on:
            - Skill development priorities
            - Networking strategies
            - Personal branding
            - Goal setting and career planning
            - Industry insights and trends
            
            Provide actionable, professional advice."""),
            ("human", f"""Provide expert career coaching for these tasks:
            {json.dumps(career_tasks)}
            
            Give specific actions for professional growth today.""")
        ])
        
        response = await llm.ainvoke(prompt.format_messages())
        
        current_advice = state.get("expert_advice", {})
        current_advice["career_coach"] = response.content
        
        return {"expert_advice": current_advice}
        
    except Exception as e:
        return {"errors": [f"Career coaching failed: {str(e)}"]}

async def time_strategist_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Time Strategist Agent Node
    
    Optimizes task scheduling and creates time blocks for efficient execution.
    """
    try:
        tasks = state.get("decomposed_tasks", [])
        available_hours = state.get("available_hours", 2.0)
        preferences = state.get("preferences", {})
        
        # Simple time blocking algorithm
        total_minutes = available_hours * 60
        time_blocks = []
        current_time = 0
        
        # Sort tasks by priority/difficulty
        sorted_tasks = sorted(tasks, key=lambda x: x.get("difficulty", 3), reverse=True)
        
        for task in sorted_tasks:
            task_duration = task.get("estimated_minutes", 30)
            if current_time + task_duration <= total_minutes:
                time_blocks.append({
                    "task_id": task.get("id", len(time_blocks)),
                    "task_name": task.get("name"),
                    "start_minute": current_time,
                    "duration_minutes": task_duration,
                    "category": task.get("category")
                })
                current_time += task_duration
        
        return {"time_blocks": time_blocks}
        
    except Exception as e:
        return {"errors": [f"Time strategy failed: {str(e)}"]}

async def daily_planner_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Daily Planner Agent Node
    
    Assembles the final daily plan by combining all agent outputs.
    """
    try:
        daily_plan = {
            "date": state.get("date"),
            "user_id": state.get("user_id"),
            "tasks": state.get("decomposed_tasks", []),
            "time_blocks": state.get("time_blocks", []),
            "expert_advice": state.get("expert_advice", {}),
            "total_estimated_time": sum(
                task.get("estimated_minutes", 0) 
                for task in state.get("decomposed_tasks", [])
            ),
            "calendar_integration_ready": True
        }
        
        return {"daily_plan": daily_plan}
        
    except Exception as e:
        return {"errors": [f"Daily planning failed: {str(e)}"]}

async def feedback_analyst_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Feedback Analyst Agent Node
    
    Processes user feedback to adapt and improve future planning.
    """
    try:
        llm = get_llm()
        
        feedback_data = state.get("feedback_data", {})
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a Feedback Analysis specialist. Analyze user feedback to improve 
            future planning. Consider:
            - Task completion rates
            - Difficulty ratings vs actual performance
            - User satisfaction patterns
            - Time estimation accuracy
            - Suggested adaptations for future plans
            
            Provide actionable insights for plan improvement."""),
            ("human", f"""Analyze this feedback and suggest adaptations:
            {json.dumps(feedback_data)}
            
            What should we change for future plans?""")
        ])
        
        response = await llm.ainvoke(prompt.format_messages())
        
        # Extract adaptations (simplified)
        adaptations = [
            "Adjust task difficulty based on completion rates",
            "Modify time estimates based on actual performance",
            "Consider user satisfaction in future planning"
        ]
        
        current_advice = state.get("expert_advice", {})
        current_advice["feedback_analyst"] = response.content
        
        return {
            "adaptations": adaptations,
            "expert_advice": current_advice
        }
        
    except Exception as e:
        return {"errors": [f"Feedback analysis failed: {str(e)}"]}
