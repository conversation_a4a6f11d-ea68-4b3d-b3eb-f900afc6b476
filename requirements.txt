# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# LangGraph and Lang<PERSON><PERSON>n for agent orchestration
langgraph==0.0.62
langchain==0.1.0
langchain-community==0.0.10
langchain-openai==0.0.2

# OpenRouter API client
openai==1.6.1
httpx==0.25.2

# Google Calendar API
google-api-python-client==2.110.0
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.2.0

# Database and ORM
sqlalchemy==2.0.23
alembic==1.13.1

# Environment and configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Vector database for embeddings (optional for future enhancements)
chromadb==0.4.18

# Utilities
python-dateutil==2.8.2
pytz==2023.3

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
